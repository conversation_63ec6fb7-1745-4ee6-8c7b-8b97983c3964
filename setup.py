from setuptools import setup, find_packages

setup(
    name='go2-webrtc-connect',
    version='1.0.0',
    author='legion1581',
    author_email='<EMAIL>',
    packages=find_packages(),
    install_requires=[
        'aiortc==1.9.0', 
        'pycryptodome',
        'opencv-python',
        'sounddevice',
        'pyaudio',
        'requests',
        'wasmtime',
        'flask-socketio',
        'lz4',
        'pydub'
    ],
)
